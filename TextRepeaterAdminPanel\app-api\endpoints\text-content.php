<?php
/**
 * Text Content Endpoint Handler
 * Handles all text content API requests for the mobile app
 */

class TextContentEndpoint {
    private $conn;
    private $response;
    private $textManager;

    public function __construct() {
        $this->conn = getAPIDatabase();
        $this->response = new APIResponse();

        // Try to load TextContentManager if it exists
        $textManagerPath = __DIR__ . '/../../classes/TextContentManager.php';
        if (file_exists($textManagerPath)) {
            require_once $textManagerPath;
            $this->textManager = new TextContentManager();
        } else {
            $this->textManager = null;
        }
    }

    /**
     * Handle text content endpoint requests
     */
    public function handle($method, $pathParts) {
        $action = $pathParts[1] ?? '';

        switch ($action) {
            case 'all':
                return $this->handleGetAll($method);

            case 'category':
                return $this->handleGetByCategory($method, $pathParts);

            case 'json':
                return $this->handleJsonFormat($method);

            case 'sync':
                return $this->handleSyncFormat($method);

            case 'stats':
                return $this->handleStats($method);

            default:
                return $this->handleJsonFormat($method); // Default to JSON format
        }
    }

    /**
     * Get all text content in JSON format (compatible with existing app)
     */
    private function handleJsonFormat($method) {
        if ($method !== 'GET') {
            return $this->response->methodNotAllowed();
        }

        try {
            if ($this->textManager) {
                $content = $this->textManager->getContentForAPI();
            } else {
                // Fallback: get content directly from database
                $content = $this->getContentDirectly();
            }

            return $this->response->success($content, 'Text content retrieved successfully');

        } catch (Exception $e) {
            error_log('Error getting text content for JSON format: ' . $e->getMessage());
            return $this->response->error('Failed to retrieve text content');
        }
    }

    /**
     * Get all text content with metadata
     */
    private function handleGetAll($method) {
        if ($method !== 'GET') {
            return $this->response->methodNotAllowed();
        }

        if (!$this->textManager) {
            return $this->response->error('Text content manager not available');
        }

        try {
            $categories = $this->textManager->getCategories(true);
            $result = [];

            foreach ($categories as $category) {
                $content = $this->textManager->getTextContent($category['id'], 1, 1000); // Get all content

                $result[] = [
                    'category' => [
                        'id' => $category['id'],
                        'name' => $category['name'],
                        'display_name' => $category['display_name'],
                        'description' => $category['description']
                    ],
                    'content' => array_map(function($item) {
                        return [
                            'id' => $item['id'],
                            'message' => $item['message'],
                            'sort_order' => $item['sort_order'],
                            'created_at' => $item['created_at']
                        ];
                    }, $content)
                ];
            }

            return $this->response->success($result, 'Text content retrieved successfully');

        } catch (Exception $e) {
            error_log('Error getting all text content: ' . $e->getMessage());
            return $this->response->error('Failed to retrieve text content');
        }
    }

    /**
     * Get text content by category
     */
    private function handleGetByCategory($method, $pathParts) {
        if ($method !== 'GET') {
            return $this->response->methodNotAllowed();
        }

        if (!$this->textManager) {
            return $this->response->error('Text content manager not available');
        }

        $categoryName = $pathParts[2] ?? '';

        if (empty($categoryName)) {
            return $this->response->badRequest('Category name is required');
        }

        try {
            // Get category by name
            $categories = $this->textManager->getCategories(true);
            $category = null;

            foreach ($categories as $cat) {
                if ($cat['name'] === $categoryName) {
                    $category = $cat;
                    break;
                }
            }

            if (!$category) {
                return $this->response->notFound('Category not found');
            }

            // Get content for this category
            $content = $this->textManager->getTextContent($category['id'], 1, 1000);

            $result = [
                'category' => [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'display_name' => $category['display_name'],
                    'description' => $category['description']
                ],
                'content' => array_map(function($item) {
                    return [
                        'id' => $item['id'],
                        'message' => $item['message'],
                        'sort_order' => $item['sort_order'],
                        'created_at' => $item['created_at']
                    ];
                }, $content),
                'total_count' => count($content)
            ];

            return $this->response->success($result, 'Category content retrieved successfully');

        } catch (Exception $e) {
            error_log('Error getting content by category: ' . $e->getMessage());
            return $this->response->error('Failed to retrieve category content');
        }
    }

    /**
     * Get text content statistics
     */
    private function handleStats($method) {
        if ($method !== 'GET') {
            return $this->response->methodNotAllowed();
        }

        if (!$this->textManager) {
            return $this->response->error('Text content manager not available');
        }

        try {
            $categories = $this->textManager->getCategories(true);
            $stats = [];
            $totalContent = 0;

            foreach ($categories as $category) {
                $count = $this->textManager->getTextContentCount($category['id']);
                $totalContent += $count;

                $stats[] = [
                    'category' => [
                        'id' => $category['id'],
                        'name' => $category['name'],
                        'display_name' => $category['display_name']
                    ],
                    'content_count' => $count
                ];
            }

            $result = [
                'total_categories' => count($categories),
                'total_content' => $totalContent,
                'categories' => $stats,
                'last_updated' => date('Y-m-d H:i:s')
            ];

            return $this->response->success($result, 'Statistics retrieved successfully');

        } catch (Exception $e) {
            error_log('Error getting text content statistics: ' . $e->getMessage());
            return $this->response->error('Failed to retrieve statistics');
        }
    }

    /**
     * Fallback method to get content directly from database
     */
    private function getContentDirectly() {
        try {
            // Check if text_content table exists
            $checkTable = "SHOW TABLES LIKE 'text_content'";
            $stmt = $this->conn->prepare($checkTable);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                // Return default content if table doesn't exist
                return [
                    'romantic' => [
                        ['id' => '1', 'message' => 'You are my sunshine ☀️'],
                        ['id' => '2', 'message' => 'I love you more than words can say 💕']
                    ],
                    'sad' => [
                        ['id' => '1', 'message' => 'Sometimes life is hard 😢'],
                        ['id' => '2', 'message' => 'Missing you so much 💔']
                    ],
                    'funny' => [
                        ['id' => '1', 'message' => 'Why did the chicken cross the road? 🐔'],
                        ['id' => '2', 'message' => 'Life is better when you are laughing 😂']
                    ]
                ];
            }

            // Get content from database
            $query = "SELECT tc.id, tc.message, cat.name as category_name
                     FROM text_content tc
                     JOIN text_categories cat ON tc.category_id = cat.id
                     WHERE tc.is_active = 1 AND cat.is_active = 1
                     ORDER BY cat.name, tc.sort_order, tc.id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $rows = $stmt->fetchAll();

            // Group by category
            $content = [
                'romantic' => [],
                'sad' => [],
                'funny' => []
            ];

            foreach ($rows as $row) {
                $categoryName = strtolower($row['category_name']);
                if (isset($content[$categoryName])) {
                    $content[$categoryName][] = [
                        'id' => $row['id'],
                        'message' => $row['message']
                    ];
                }
            }

            return $content;

        } catch (Exception $e) {
            error_log('Error in getContentDirectly: ' . $e->getMessage());

            // Return minimal default content on error
            return [
                'romantic' => [['id' => '1', 'message' => 'Love is beautiful 💕']],
                'sad' => [['id' => '1', 'message' => 'Stay strong 💪']],
                'funny' => [['id' => '1', 'message' => 'Keep smiling 😊']]
            ];
        }
    }

    /**
     * Handle sync format - returns complete data for app synchronization
     */
    private function handleSyncFormat($method) {
        if ($method !== 'GET') {
            return $this->response->methodNotAllowed();
        }

        try {
            // Get all categories
            $categoriesQuery = "SELECT id, name, display_name, description, sort_order, is_active
                               FROM text_categories
                               WHERE is_active = 1
                               ORDER BY sort_order, name";

            $stmt = $this->conn->prepare($categoriesQuery);
            $stmt->execute();
            $categories = $stmt->fetchAll();

            // Get all text content
            $contentQuery = "SELECT tc.id, tc.message, tc.category_id, tc.sort_order,
                                   cat.name as category_name, cat.display_name as category_display_name
                            FROM text_content tc
                            JOIN text_categories cat ON tc.category_id = cat.id
                            WHERE tc.is_active = 1 AND cat.is_active = 1
                            ORDER BY cat.sort_order, cat.name, tc.sort_order, tc.id";

            $stmt = $this->conn->prepare($contentQuery);
            $stmt->execute();
            $content = $stmt->fetchAll();

            // Format categories for Android app
            $formattedCategories = [];
            foreach ($categories as $category) {
                $formattedCategories[] = [
                    'id' => (int)$category['id'],
                    'name' => $category['name'],
                    'displayName' => $category['display_name'],
                    'description' => $category['description'] ?? '',
                    'sortOrder' => (int)$category['sort_order']
                ];
            }

            // Group content by category for legacy compatibility
            $legacyContent = [
                'romantic' => [],
                'sad' => [],
                'funny' => []
            ];

            // Format content for Android app
            $dynamicContent = [];
            foreach ($content as $item) {
                $categoryName = strtolower($item['category_name']);

                $messageData = [
                    'id' => $item['id'],
                    'message' => $item['message']
                ];

                // Add to legacy format for backward compatibility
                if (isset($legacyContent[$categoryName])) {
                    $legacyContent[$categoryName][] = $messageData;
                }

                // Add to dynamic format
                if (!isset($dynamicContent[$categoryName])) {
                    $dynamicContent[$categoryName] = [];
                }
                $dynamicContent[$categoryName][] = [
                    'id' => $item['id'],
                    'message' => $item['message'],
                    'categoryId' => (int)$item['category_id'],
                    'categoryName' => $item['category_name'],
                    'sortOrder' => (int)$item['sort_order']
                ];
            }

            // Prepare sync response
            $syncData = [
                'categories' => $formattedCategories,
                'content' => $dynamicContent,
                'legacy' => $legacyContent, // For backward compatibility
                'sync_timestamp' => date('Y-m-d H:i:s'),
                'total_categories' => count($formattedCategories),
                'total_messages' => count($content)
            ];

            return $this->response->success($syncData, 'Sync data retrieved successfully');

        } catch (Exception $e) {
            error_log('Error in handleSyncFormat: ' . $e->getMessage());
            return $this->response->error('Failed to retrieve sync data');
        }
    }
}
?>
