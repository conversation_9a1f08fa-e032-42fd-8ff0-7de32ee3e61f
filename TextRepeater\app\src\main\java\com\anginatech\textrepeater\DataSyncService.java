package com.anginatech.textrepeater;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.anginatech.textrepeater.models.ApiResponse;
import com.anginatech.textrepeater.models.Category;
import com.anginatech.textrepeater.models.SyncResponse;
import com.anginatech.textrepeater.models.TextContentResponse;
import com.anginatech.textrepeater.network.ApiService;
import com.anginatech.textrepeater.network.ModernApiClient;
import com.anginatech.textrepeater.network.NetworkUtils;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Data Synchronization Service
 * Handles automatic background synchronization of categories and text content
 */
public class DataSyncService {
    private static final String TAG = "DataSyncService";
    
    private final Context context;
    private final DynamicDatabaseHelper dynamicDatabaseHelper;
    private final DatabaseHelper databaseHelper;
    private final ApiService apiService;
    private final SharedPreferences syncPrefs;
    
    // Sync state tracking
    private boolean isSyncing = false;
    private SyncCallback currentCallback;
    
    /**
     * Callback interface for sync operations
     */
    public interface SyncCallback {
        void onSyncStarted();
        void onSyncProgress(String message, int progress);
        void onSyncSuccess(SyncResponse data);
        void onSyncError(String error);
        void onSyncComplete();
    }
    
    public DataSyncService(Context context) {
        this.context = context;
        this.dynamicDatabaseHelper = new DynamicDatabaseHelper(context);
        this.databaseHelper = new DatabaseHelper(context);
        this.apiService = ModernApiClient.getInstance(context).getApiService();
        this.syncPrefs = context.getSharedPreferences(Config.PREFS_APP_CONFIG, Context.MODE_PRIVATE);
    }
    
    /**
     * Check if sync is needed based on network availability and last sync time
     */
    public boolean isSyncNeeded() {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.d(TAG, "Network not available, sync not needed");
            return false;
        }
        
        if (!isSyncEnabled()) {
            Log.d(TAG, "Sync disabled, sync not needed");
            return false;
        }
        
        long lastSyncTime = getLastSyncTime();
        long currentTime = System.currentTimeMillis();
        long timeSinceLastSync = currentTime - lastSyncTime;
        
        boolean syncNeeded = timeSinceLastSync > Config.SYNC_CACHE_DURATION;
        Log.d(TAG, "Sync needed: " + syncNeeded + " (time since last sync: " + timeSinceLastSync + "ms)");
        
        return syncNeeded;
    }
    
    /**
     * Perform automatic sync if needed
     */
    public void performAutoSync(SyncCallback callback) {
        if (isSyncing) {
            Log.w(TAG, "Sync already in progress");
            if (callback != null) {
                callback.onSyncError("Sync already in progress");
            }
            return;
        }
        
        if (!isSyncNeeded()) {
            Log.d(TAG, "Auto sync not needed");
            if (callback != null) {
                callback.onSyncComplete();
            }
            return;
        }
        
        performSync(callback);
    }
    
    /**
     * Force sync regardless of cache status
     */
    public void performForceSync(SyncCallback callback) {
        if (isSyncing) {
            Log.w(TAG, "Sync already in progress");
            if (callback != null) {
                callback.onSyncError("Sync already in progress");
            }
            return;
        }
        
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.w(TAG, "Network not available for force sync");
            if (callback != null) {
                callback.onSyncError("Network not available");
            }
            return;
        }
        
        performSync(callback);
    }
    
    /**
     * Perform the actual sync operation
     */
    private void performSync(SyncCallback callback) {
        isSyncing = true;
        currentCallback = callback;
        
        Log.d(TAG, "Starting data synchronization...");
        
        if (callback != null) {
            callback.onSyncStarted();
            callback.onSyncProgress("Connecting to server...", 10);
        }
        
        // Make API call to get sync data
        Call<ApiResponse<SyncResponse>> call = apiService.getSyncData(Config.ENDPOINT_SYNC_DATA);
        
        call.enqueue(new Callback<ApiResponse<SyncResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<SyncResponse>> call, Response<ApiResponse<SyncResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<SyncResponse> apiResponse = response.body();
                    
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        Log.d(TAG, "Sync data received successfully");
                        if (currentCallback != null) {
                            currentCallback.onSyncProgress("Processing data...", 50);
                        }
                        
                        // Process the sync data
                        processSyncData(apiResponse.getData());
                    } else {
                        String error = apiResponse.getMessage() != null ? apiResponse.getMessage() : "Unknown API error";
                        Log.e(TAG, "API error: " + error);
                        handleSyncError("Server error: " + error);
                    }
                } else {
                    String error = "HTTP " + response.code() + ": " + response.message();
                    Log.e(TAG, "HTTP error: " + error);
                    handleSyncError("Network error: " + error);
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<SyncResponse>> call, Throwable t) {
                Log.e(TAG, "Sync request failed: " + t.getMessage(), t);
                handleSyncError("Connection failed: " + t.getMessage());
            }
        });
    }
    
    /**
     * Process the received sync data - COMPLETE DATA REPLACEMENT
     */
    private void processSyncData(SyncResponse syncData) {
        try {
            Log.d(TAG, "Processing FRESH sync data: " + syncData.toString());

            if (currentCallback != null) {
                currentCallback.onSyncProgress("Clearing old data...", 60);
            }

            // COMPLETE DATA REPLACEMENT - Clear ALL existing data
            clearAllExistingData();

            if (currentCallback != null) {
                currentCallback.onSyncProgress("Saving fresh categories...", 75);
            }

            // Save ONLY current categories from server
            List<Category> categories = syncData.getCategories();
            if (categories != null && !categories.isEmpty()) {
                for (Category category : categories) {
                    dynamicDatabaseHelper.insertOrUpdateCategory(category);
                }
                Log.d(TAG, "Saved " + categories.size() + " FRESH categories");
            } else {
                Log.w(TAG, "No categories received from server");
            }

            if (currentCallback != null) {
                currentCallback.onSyncProgress("Saving fresh messages...", 90);
            }

            // Save ONLY current content from server
            Map<String, List<SyncResponse.SyncTextMessage>> content = syncData.getContent();
            if (content != null && !content.isEmpty()) {
                int totalMessages = 0;
                for (Map.Entry<String, List<SyncResponse.SyncTextMessage>> entry : content.entrySet()) {
                    String categoryName = entry.getKey();
                    List<SyncResponse.SyncTextMessage> messages = entry.getValue();

                    if (messages != null) {
                        for (SyncResponse.SyncTextMessage message : messages) {
                            dynamicDatabaseHelper.insertOrUpdateMessage(
                                message.getId(),
                                message.getMessage(),
                                categoryName
                            );
                            totalMessages++;
                        }
                    }
                }
                Log.d(TAG, "Saved " + totalMessages + " FRESH messages");
            } else {
                Log.w(TAG, "No content received from server");
            }

            // NO LEGACY SUPPORT - Only fresh data

            // Update sync timestamp
            updateLastSyncTime();

            if (currentCallback != null) {
                currentCallback.onSyncProgress("Fresh data sync completed", 100);
                currentCallback.onSyncSuccess(syncData);
            }

            Log.d(TAG, "FRESH data synchronization completed successfully - NO legacy data");

        } catch (Exception e) {
            Log.e(TAG, "Error processing sync data: " + e.getMessage(), e);
            handleSyncError("Failed to save fresh data: " + e.getMessage());
        } finally {
            completeSyncOperation();
        }
    }
    
    /**
     * Clear ALL existing data from both databases - COMPLETE REPLACEMENT
     */
    private void clearAllExistingData() {
        try {
            Log.d(TAG, "Clearing ALL existing data for fresh sync");

            // Clear dynamic database (categories and messages)
            dynamicDatabaseHelper.clearAllData();

            // Clear legacy database (romantic, sad, funny tables)
            databaseHelper.clearAllData();

            Log.d(TAG, "All existing data cleared successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error clearing existing data: " + e.getMessage(), e);
            throw e; // Re-throw to handle in calling method
        }
    }
    
    /**
     * Handle sync errors
     */
    private void handleSyncError(String error) {
        Log.e(TAG, "Sync error: " + error);
        
        if (currentCallback != null) {
            currentCallback.onSyncError(error);
        }
        
        completeSyncOperation();
    }
    
    /**
     * Complete sync operation and cleanup
     */
    private void completeSyncOperation() {
        isSyncing = false;
        
        if (currentCallback != null) {
            currentCallback.onSyncComplete();
            currentCallback = null;
        }
    }
    
    // Utility methods
    public boolean isSyncEnabled() {
        return syncPrefs.getBoolean(Config.PREF_SYNC_ENABLED, true);
    }
    
    public void setSyncEnabled(boolean enabled) {
        syncPrefs.edit().putBoolean(Config.PREF_SYNC_ENABLED, enabled).apply();
    }
    
    public long getLastSyncTime() {
        return syncPrefs.getLong(Config.PREF_LAST_SYNC_TIME, 0);
    }
    
    private void updateLastSyncTime() {
        syncPrefs.edit().putLong(Config.PREF_LAST_SYNC_TIME, System.currentTimeMillis()).apply();
    }
    
    public boolean isSyncing() {
        return isSyncing;
    }
}
