package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Sync Response Model for complete data synchronization
 */
public class SyncResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @SerializedName("categories")
    private List<Category> categories;

    @SerializedName("content")
    private Map<String, List<SyncTextMessage>> content;

    @SerializedName("legacy")
    private LegacyContent legacy;

    @SerializedName("sync_timestamp")
    private String syncTimestamp;

    @SerializedName("total_categories")
    private int totalCategories;

    @SerializedName("total_messages")
    private int totalMessages;

    // Constructors
    public SyncResponse() {}

    public SyncResponse(List<Category> categories, Map<String, List<SyncTextMessage>> content, 
                       LegacyContent legacy, String syncTimestamp, int totalCategories, int totalMessages) {
        this.categories = categories;
        this.content = content;
        this.legacy = legacy;
        this.syncTimestamp = syncTimestamp;
        this.totalCategories = totalCategories;
        this.totalMessages = totalMessages;
    }

    // Getters and Setters
    public List<Category> getCategories() {
        return categories;
    }

    public void setCategories(List<Category> categories) {
        this.categories = categories;
    }

    public Map<String, List<SyncTextMessage>> getContent() {
        return content;
    }

    public void setContent(Map<String, List<SyncTextMessage>> content) {
        this.content = content;
    }

    public LegacyContent getLegacy() {
        return legacy;
    }

    public void setLegacy(LegacyContent legacy) {
        this.legacy = legacy;
    }

    public String getSyncTimestamp() {
        return syncTimestamp;
    }

    public void setSyncTimestamp(String syncTimestamp) {
        this.syncTimestamp = syncTimestamp;
    }

    public int getTotalCategories() {
        return totalCategories;
    }

    public void setTotalCategories(int totalCategories) {
        this.totalCategories = totalCategories;
    }

    public int getTotalMessages() {
        return totalMessages;
    }

    public void setTotalMessages(int totalMessages) {
        this.totalMessages = totalMessages;
    }

    /**
     * Sync Text Message model with additional metadata
     */
    public static class SyncTextMessage implements Serializable {
        private static final long serialVersionUID = 1L;

        @SerializedName("id")
        private String id;

        @SerializedName("message")
        private String message;

        @SerializedName("categoryId")
        private int categoryId;

        @SerializedName("categoryName")
        private String categoryName;

        @SerializedName("sortOrder")
        private int sortOrder;

        public SyncTextMessage() {}

        public SyncTextMessage(String id, String message, int categoryId, String categoryName, int sortOrder) {
            this.id = id;
            this.message = message;
            this.categoryId = categoryId;
            this.categoryName = categoryName;
            this.sortOrder = sortOrder;
        }

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(int categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public int getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(int sortOrder) {
            this.sortOrder = sortOrder;
        }

        /**
         * Convert to TextMessage for compatibility
         */
        public TextMessage toTextMessage() {
            TextMessage textMessage = new TextMessage();
            textMessage.setId(Integer.parseInt(id));
            textMessage.setMessage(message);
            textMessage.setCategoryName(categoryName);
            return textMessage;
        }
    }

    /**
     * Legacy content structure for backward compatibility
     */
    public static class LegacyContent implements Serializable {
        private static final long serialVersionUID = 1L;

        @SerializedName("romantic")
        private List<TextContentResponse.TextMessage> romantic;

        @SerializedName("sad")
        private List<TextContentResponse.TextMessage> sad;

        @SerializedName("funny")
        private List<TextContentResponse.TextMessage> funny;

        public LegacyContent() {}

        public List<TextContentResponse.TextMessage> getRomantic() {
            return romantic;
        }

        public void setRomantic(List<TextContentResponse.TextMessage> romantic) {
            this.romantic = romantic;
        }

        public List<TextContentResponse.TextMessage> getSad() {
            return sad;
        }

        public void setSad(List<TextContentResponse.TextMessage> sad) {
            this.sad = sad;
        }

        public List<TextContentResponse.TextMessage> getFunny() {
            return funny;
        }

        public void setFunny(List<TextContentResponse.TextMessage> funny) {
            this.funny = funny;
        }
    }

    @Override
    public String toString() {
        return "SyncResponse{" +
                "categories=" + (categories != null ? categories.size() : 0) +
                ", content=" + (content != null ? content.size() : 0) +
                ", syncTimestamp='" + syncTimestamp + '\'' +
                ", totalCategories=" + totalCategories +
                ", totalMessages=" + totalMessages +
                '}';
    }
}
